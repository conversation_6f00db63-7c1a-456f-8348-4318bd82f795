using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Sockets;
using System.Text.Json;
using System.Threading.Tasks;
using Furion.FriendlyException;
using IotGateway.FtpSiemensImplant.Core;
using IotGateway.FtpSiemensImplant.Models;
using IotGateway.Plugin.Core;
using IotGateway.Plugin.Core.Models;
using Microsoft.Extensions.Logging;

namespace IotGateway.FtpSiemensImplant;

/// <summary>
///     FTP服务器插件
/// </summary>
public class FtpSiemensImplantServerPlugin : PluginBase
{
    /// <summary>
    ///     配置文件路径
    /// </summary>
    private readonly string _configPath;

    /// <summary>
    ///     配置
    /// </summary>
    private readonly FtpSiemensImplantConfig _siemensImplantConfig;

    /// <summary>
    ///     配置属性
    /// </summary>
    public object? Configuration => _siemensImplantConfig;

    /// <summary>
    ///     FTP服务器
    /// </summary>
    private FtpSiemensImplantServer? _ftpServer;

    /// <summary>
    ///     日志记录器
    /// </summary>
    private ILogger<FtpSiemensImplantServerPlugin>? _logger;

    /// <summary>
    ///     FTP会话日志记录器
    /// </summary>
    private ILogger<FtpSiemensImplantSession>? _sessionLogger;

    /// <summary>
    ///     获取配置
    /// </summary>
    public FtpSiemensImplantConfig SiemensImplantConfig => _siemensImplantConfig;

    /// <summary>
    ///     构造函数
    /// </summary>
    public FtpSiemensImplantServerPlugin()
    {
        try
        {
            _configPath = Path.Combine(AppContext.BaseDirectory, "Configs", "siemensimplant.json");
            _siemensImplantConfig = LoadConfiguration() ?? new FtpSiemensImplantConfig();
            _configuration = _siemensImplantConfig;

            // 延迟创建日志记录器
            InitLogger();
        }
        catch (Exception ex)
        {
            // 确保构造函数不会抛出异常
            Console.WriteLine($"FTP插件初始化失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     初始化日志记录器
    /// </summary>
    private void InitLogger()
    {
        try
        {
            var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole()
                    .SetMinimumLevel(LogLevel.Debug);
            });
            _logger = loggerFactory.CreateLogger<FtpSiemensImplantServerPlugin>();
            _sessionLogger = loggerFactory.CreateLogger<FtpSiemensImplantSession>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"日志初始化失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     加载配置
    /// </summary>
    private FtpSiemensImplantConfig? LoadConfiguration()
    {
        try
        {
            if (File.Exists(_configPath))
            {
                var json = File.ReadAllText(_configPath);
                var config = JsonSerializer.Deserialize<FtpSiemensImplantConfig>(json);
                return config;
            }

            _logger?.LogWarning("FTP配置文件不存在，将使用默认配置");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "加载FTP配置失败");
        }

        return null;
    }

    /// <summary>
    ///     保存配置
    /// </summary>
    private void SaveConfiguration()
    {
        try
        {
            _logger?.LogInformation("正在保存FTP配置...");
            var directory = Path.GetDirectoryName(_configPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
                _logger?.LogInformation("创建配置目录: {Directory}", directory);
            }

            var json = JsonSerializer.Serialize(_siemensImplantConfig, new JsonSerializerOptions
            {
                WriteIndented = true
            });
            File.WriteAllText(_configPath, json);
            _logger?.LogInformation("FTP配置保存成功");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "保存FTP配置失败");
        }
    }

    /// <summary>
    ///     插件名称
    /// </summary>
    public override string Name => "FTP Siemens Implant Server";

    /// <summary>
    ///     插件版本
    /// </summary>
    public override string Version => "1.0.0";

    /// <summary>
    ///     插件描述
    /// </summary>
    public override string Description => "西门子Ftp服务(植入版)";

    /// <summary>
    ///     插件分类
    /// </summary>
    public override string Category => "Dnc";

    /// <summary>
    ///     初始化
    /// </summary>
    public override async Task InitializeAsync()
    {
        try
        {
            _logger?.LogInformation("FTP初始化开始: {SiemensImplantConfig}", JsonSerializer.Serialize(_siemensImplantConfig));
            // 如果自启动
            if (_siemensImplantConfig.Enabled)
            {
                _logger?.LogInformation("FTP服务器配置为自启动，准备启动服务器");
                await StartAsync();
            }

            await base.InitializeAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "FTP服务器初始化失败");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     启动
    /// </summary>
    /// <returns></returns>
    public override async Task StartAsync()
    {
        try
        {
            Console.WriteLine("正在启动FTP服务器...");
            _logger?.LogInformation("正在启动FTP服务器...");

            if (_ftpServer != null)
            {
                Console.WriteLine("停止已有FTP服务器");
                await StopAsync();

                // 添加短暂延迟，确保端口完全释放
                await Task.Delay(1000);
            }

            // 检查端口是否可用
            if (!IsPortAvailable(_siemensImplantConfig.Port))
            {
                var errorMessage = $"端口 {_siemensImplantConfig.Port} 已被占用，无法启动FTP服务器";
                _logger?.LogError(errorMessage);
                Console.WriteLine(errorMessage);
                throw Oops.Oh(errorMessage);
            }

            // 创建并启动FTP服务器
            _ftpServer = new FtpSiemensImplantServer(this, _siemensImplantConfig, _sessionLogger);
            await _ftpServer.StartAsync();

            _logger?.LogInformation("FTP服务器启动成功");
            Console.WriteLine("FTP服务器启动成功");

            await base.StartAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"FTP服务器启动失败: {ex.Message}");
            _logger?.LogError(ex, "FTP服务器启动失败");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    /// 检查端口是否可用
    /// </summary>
    /// <param name="port">要检查的端口</param>
    /// <returns>如果端口可用返回true，否则返回false</returns>
    private bool IsPortAvailable(int port)
    {
        try
        {
            // 尝试在指定端口上创建一个TCP监听器
            using var socket = new Socket(
                AddressFamily.InterNetwork,
                SocketType.Stream,
                ProtocolType.Tcp);

            socket.SetSocketOption(
                SocketOptionLevel.Socket,
                SocketOptionName.ReuseAddress,
                true);

            socket.Bind(new System.Net.IPEndPoint(System.Net.IPAddress.Any, port));
            socket.Close();
            return true;
        }
        catch (SocketException)
        {
            return false;
        }
    }

    /// <summary>
    ///     停止
    /// </summary>
    /// <returns></returns>
    public override async Task StopAsync()
    {
        _logger?.LogInformation("正在停止FTP服务器...");

        try
        {
            if (_ftpServer != null)
            {
                try
                {
                    // 添加短暂延迟，确保所有连接都有机会完成
                    await Task.Delay(500);

                    // 停止FTP服务器
                    await _ftpServer.StopAsync();
                }
                catch (OperationCanceledException)
                {
                    _logger?.LogWarning("停止FTP服务器超时，强制终止");
                }
                catch (SocketException ex)
                {
                    // 特别处理"Address already in use"错误
                    _logger?.LogWarning("停止FTP服务器时出现地址占用错误: {Message}", ex.Message);
                }

                _ftpServer = null;
                _logger?.LogInformation("FTP服务器已停止");
            }

            // 确保垃圾回收运行，释放所有未使用的资源
            GC.Collect();
            GC.WaitForPendingFinalizers();

            await base.StopAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "停止FTP服务器时发生错误");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     更新配置
    /// </summary>
    /// <param name="configuration"></param>
    /// <returns></returns>
    public override async Task UpdateConfigurationAsync(object configuration)
    {
        try
        {
            if (configuration is JsonElement jsonElement)
            {
                // 将JsonElement反序列化为FtpConfig对象
                var config = jsonElement.Deserialize<FtpSiemensImplantConfig>(new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (config != null)
                {
                    _siemensImplantConfig.Port = config.Port; // 端口
                    _siemensImplantConfig.Enabled = config.Enabled; // 是否启用
                    _siemensImplantConfig.SendPath = config.SendPath; // 发送文件路径
                    _siemensImplantConfig.ReceivePath = config.ReceivePath; // 接收文件路径
                    _siemensImplantConfig.ScpHostname = config.ScpHostname;
                    _siemensImplantConfig.ScpPort = config.ScpPort;
                    _siemensImplantConfig.ScpUsername = config.ScpUsername;
                    _siemensImplantConfig.ScpPassword = config.ScpPassword;
                    _siemensImplantConfig.ScpWorkPath = config.ScpWorkPath;

                    // 保存更新后的配置
                    SaveConfiguration();

                    await StopAsync();
                    // 如果服务正在运行，需要重启以应用新配置
                    if (_siemensImplantConfig.Enabled)
                    {
                        await StartAsync();
                    }
                }
            }
            else if (configuration is FtpSiemensImplantConfig config)
            {
                // 保持原有的处理逻辑
                _siemensImplantConfig.Port = config.Port;
                _siemensImplantConfig.Enabled = config.Enabled;
                _siemensImplantConfig.SendPath = config.SendPath;
                _siemensImplantConfig.ReceivePath = config.ReceivePath;
                _siemensImplantConfig.ScpHostname = config.ScpHostname;
                _siemensImplantConfig.ScpPort = config.ScpPort;
                _siemensImplantConfig.ScpUsername = config.ScpUsername;
                _siemensImplantConfig.ScpPassword = config.ScpPassword;
                _siemensImplantConfig.ScpWorkPath = config.ScpWorkPath;

                SaveConfiguration(); // 保存配置
                await StopAsync();

                if (_siemensImplantConfig.Enabled)
                {
                    await StartAsync();
                }
            }

            await base.UpdateConfigurationAsync(configuration); // 更新配置
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "更新FTP配置失败");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    /// 获取用户特定的文件路径
    /// </summary>
    /// <param name="siemensImplantUser">用户信息</param>
    /// <param name="pathTemplate">路径模板</param>
    /// <returns>完整的文件路径</returns>
    public string GetUserSpecificPath(FtpSiemensImplantUser siemensImplantUser, string pathTemplate)
    {
        // 替换路径中的占位符
        string path = string.Format(pathTemplate, siemensImplantUser.DeviceCode);
        // 返回完整路径
        return Path.Combine(AppContext.BaseDirectory, path);
    }

    /// <summary>
    /// 确保目录存在
    /// </summary>
    /// <param name="path">目录路径</param>
    public void EnsureDirectoryExists(string path)
    {
        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
            _logger?.LogInformation("创建目录: {Path}", path);
        }
    }

    /// <summary>
    /// 设置DNC配置
    /// </summary>
    /// <param name="dncConfig">DNC配置</param>
    /// <returns>设置结果</returns>
    public override async Task SetDncConfigAsync(DncConfig dncConfig)
    {
        // 先调用基类方法，将配置添加到_dncConfigs中
        await base.SetDncConfigAsync(dncConfig);

        // 如果配置是启用的，为这个配置创建目录
        if (dncConfig.Enabled)
        {
            // 确保用户的发送和接收目录存在
            string sendPath = GetUserSpecificPath(new FtpSiemensImplantUser
            {
                DeviceCode = dncConfig.DeviceCode,
                Username = dncConfig.Username,
                IsAuthenticated = true
            }, _siemensImplantConfig.SendPath);

            string recPath = GetUserSpecificPath(new FtpSiemensImplantUser
            {
                DeviceCode = dncConfig.DeviceCode,
                Username = dncConfig.Username,
                IsAuthenticated = true
            }, _siemensImplantConfig.ReceivePath);

            EnsureDirectoryExists(sendPath);
            EnsureDirectoryExists(recPath);
            _logger?.LogInformation("用户 {DeviceCode} 的目录创建完成，发送目录: {SendPath}, 接收目录: {RecPath}",
                dncConfig.DeviceCode, sendPath, recPath);
        }
    }

    /// <summary>
    /// 获取FTP服务器文件列表
    /// </summary>
    /// <param name="configId">配置ID</param>
    /// <param name="folderType">文件夹类型(SEND/REC)</param>
    /// <returns>以文件夹为键，文件信息列表为值的字典</returns>
    public override async Task<Dictionary<string, List<FtpFileInfo>>> GetFtpFiles(string? configId = null, string? folderType = null)
    {
        var result = new Dictionary<string, List<FtpFileInfo>>();

        try
        {
            // 获取DNC配置列表
            var dncConfigs = await GetDncConfigsAsync();

            // 如果指定了configId，只处理该配置
            if (!string.IsNullOrEmpty(configId))
            {
                var targetConfig = dncConfigs.FirstOrDefault(c => c.Id.ToString() == configId);
                if (targetConfig != null)
                {
                    dncConfigs = new List<DncConfig> { targetConfig };
                }
                else
                {
                    _logger?.LogWarning("未找到指定的配置ID: {ConfigId}", configId);
                    return result;
                }
            }

            // 遍历每个DNC配置
            foreach (var dncConfig in dncConfigs.Where(c => c.Enabled))
            {
                // 处理SEND文件夹
                if (string.IsNullOrEmpty(folderType) || folderType.ToUpper() == "SEND")
                {
                    var sendPath = GetUserSpecificPath(new FtpSiemensImplantUser
                    {
                        DeviceCode = dncConfig.DeviceCode,
                        Username = dncConfig.Username,
                        IsAuthenticated = true
                    }, _siemensImplantConfig.SendPath);

                    var sendFiles = GetFilesFromDirectory(sendPath, "SEND", dncConfig);
                    if (sendFiles.Any())
                    {
                        var key = $"{dncConfig.DeviceCode}/SEND";
                        result[key] = sendFiles;
                    }
                }

                // 处理REC文件夹
                if (string.IsNullOrEmpty(folderType) || folderType.ToUpper() == "REC")
                {
                    var recPath = GetUserSpecificPath(new FtpSiemensImplantUser
                    {
                        DeviceCode = dncConfig.DeviceCode,
                        Username = dncConfig.Username,
                        IsAuthenticated = true
                    }, _siemensImplantConfig.ReceivePath);

                    var recFiles = GetFilesFromDirectory(recPath, "REC", dncConfig);
                    if (recFiles.Any())
                    {
                        var key = $"{dncConfig.DeviceCode}/REC";
                        result[key] = recFiles;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "获取FTP文件列表时出错");
        }

        return result;
    }

    /// <summary>
    /// 从指定目录获取文件列表
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <param name="folderType">文件夹类型</param>
    /// <param name="dncConfig">DNC配置</param>
    /// <returns>文件信息列表</returns>
    private List<FtpFileInfo> GetFilesFromDirectory(string directoryPath, string folderType, DncConfig dncConfig)
    {
        var fileList = new List<FtpFileInfo>();

        try
        {
            // 确保目录存在
            if (!Directory.Exists(directoryPath))
            {
                EnsureDirectoryExists(directoryPath);
                return fileList; // 新创建的目录为空
            }

            // 获取目录中的所有文件
            var files = Directory.GetFiles(directoryPath);

            // 处理每个文件，提取详细信息
            foreach (var file in files)
            {
                try
                {
                    var fileInfo = new FileInfo(file);

                    // 创建文件信息模型
                    var fileModel = new FtpFileInfo
                    {
                        FileName = fileInfo.Name,
                        ModifiedTime = fileInfo.LastWriteTime,
                        Size = FormatFileSize(fileInfo.Length),
                        FileType = fileInfo.Extension,
                        FullPath = fileInfo.FullName,
                        Source = directoryPath,
                        ConfigId = dncConfig.Id.ToString(),
                        DeviceCode = dncConfig.DeviceCode,
                        FolderType = folderType
                    };

                    fileList.Add(fileModel);
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "处理文件时出错: {FilePath}", file);
                }
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "读取目录时出错: {DirectoryPath}", directoryPath);
        }

        return fileList;
    }

    /// <summary>
    /// 格式化文件大小
    /// </summary>
    /// <param name="bytes">字节数</param>
    /// <returns>格式化后的文件大小</returns>
    private string FormatFileSize(long bytes)
    {
        if (bytes < 1024)
            return $"{bytes} B";
        else if (bytes < 1024 * 1024)
            return $"{bytes / 1024.0:F1} KB";
        else if (bytes < 1024 * 1024 * 1024)
            return $"{bytes / (1024.0 * 1024.0):F1} MB";
        else
            return $"{bytes / (1024.0 * 1024.0 * 1024.0):F1} GB";
    }

    /// <summary>
    /// 获取插件配置
    /// </summary>
    /// <returns>FTP西门子植入版服务器配置</returns>
    public FtpSiemensImplantConfig GetConfig()
    {
        return _siemensImplantConfig;
    }
}