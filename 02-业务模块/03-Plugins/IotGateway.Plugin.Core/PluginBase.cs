using IotGateway.Plugin.Core.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace IotGateway.Plugin.Core;

/// <summary>
/// 插件基类
/// </summary>
public abstract class PluginBase : IPlugin
{
  /// <summary>
  /// 是否启用  
  /// </summary>
  protected bool _enabled;

  /// <summary>
  /// 配置
  /// </summary>
  protected object _configuration;

  /// <summary>
  /// DNC配置集合
  /// </summary>
  protected List<DncConfig> _dncConfigs = new List<DncConfig>();

  /// <summary>
  /// 插件名称
  /// </summary>
  public abstract string Name { get; }

  /// <summary>
  /// 插件版本
  /// </summary>
  public abstract string Version { get; }

  /// <summary>
  /// 插件描述
  /// </summary>
  public abstract string Description { get; }

  /// <summary>
  /// 插件分类
  /// </summary>
  public virtual string Category { get; } = "通用";

  /// <summary>
  /// 是否启用
  /// </summary>
  public bool Enabled => _enabled;

  /// <summary>
  /// 初始化
  /// </summary>
  public virtual async Task InitializeAsync()
  {
    // 基础初始化逻辑
    await Task.CompletedTask;
  }

  /// <summary>
  /// 启动
  /// </summary>
  public virtual async Task StartAsync()
  {
    _enabled = true;
    await Task.CompletedTask;
  }

  /// <summary>
  /// 停止
  /// </summary>
  public virtual async Task StopAsync()
  {
    _enabled = false;
    await Task.CompletedTask;
  }

  /// <summary>
  /// 获取配置
  /// </summary>
  public virtual async Task<object> GetConfigurationAsync()
  {
    return await Task.FromResult(_configuration);
  }

  /// <summary>
  /// 获取配置Schema
  /// </summary>
  public virtual async Task<object?> GetConfigurationSchemaAsync()
  {
    // 先尝试调用GetConfigurationSchema方法
    if (_configuration is IPluginConfig config)
    {
      var getSchema = config.GetConfigurationSchema();
      if (getSchema != null)
        return await Task.FromResult(getSchema);
    }
    return null;
  }

  /// <summary>
  /// 更新配置
  /// </summary>
  public virtual async Task UpdateConfigurationAsync(object configuration)
  {
    if (_configuration != null)
    {
      var configType = _configuration.GetType();
      if (configuration is System.Text.Json.JsonElement jsonElement)
      {
        // 如果是 JsonElement，将其转换为原始配置类型
        _configuration = System.Text.Json.JsonSerializer.Deserialize(
          jsonElement.GetRawText(),
          configType,
          new System.Text.Json.JsonSerializerOptions
          {
            PropertyNameCaseInsensitive = true
          }
        );
      }
      else
      {
        // 如果不是 JsonElement，尝试直接转换
        _configuration = System.Text.Json.JsonSerializer.Deserialize(
          System.Text.Json.JsonSerializer.Serialize(configuration),
          configType,
          new System.Text.Json.JsonSerializerOptions
          {
            PropertyNameCaseInsensitive = true
          }
        );
      }
    }
    else
    {
      _configuration = configuration;
    }

    await Task.CompletedTask;
  }

  /// <summary>
  /// 启用或禁用插件
  /// </summary>
  /// <param name="enable">是否启用</param>
  /// <returns>操作结果</returns>
  public virtual async Task ToggleEnableAsync(bool enable)
  {
    if (enable && !_enabled)
    {
      // 如果需要启用且当前未启用，则启动插件
      await StartAsync();
    }
    else if (!enable && _enabled)
    {
      // 如果需要禁用且当前已启用，则停止插件
      await StopAsync();
    }

    // 更新配置中的启用状态（如果配置实现了IPluginConfig接口）
    if (_configuration is IPluginConfig config)
    {
      config.Enabled = enable;

      // 更新配置
      await UpdateConfigurationAsync(_configuration);
    }
  }

  /// <summary>
  /// 获取FTP服务器文件列表
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="folderType">文件夹类型(SEND/REC)</param>
  /// <returns>以文件夹为键，文件信息列表为值的字典</returns>
  public virtual async Task<Dictionary<string, List<FtpFileInfo>>> GetFtpFiles(string? configId = null, string? folderType = null)
  {
    var result = new Dictionary<string, List<FtpFileInfo>>();
    var pluginName = GetType().Name;

    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 开始获取FTP文件列表");
    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 参数 - configId: {configId ?? "null"}, folderType: {folderType ?? "null"}");

    try
    {
      // 获取DNC配置列表
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 正在获取DNC配置列表...");
      var dncConfigs = await GetDncConfigsAsync();
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 获取到 {dncConfigs.Count} 个DNC配置");

      // 如果指定了configId，只处理该配置
      if (!string.IsNullOrEmpty(configId))
      {
        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 筛选指定配置ID: {configId}");
        var targetConfig = dncConfigs.FirstOrDefault(c => c.Id.ToString() == configId);
        if (targetConfig != null)
        {
          dncConfigs = new List<DncConfig> { targetConfig };
          Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 找到目标配置: {targetConfig.DeviceCode} ({targetConfig.DeviceName})");
        }
        else
        {
          Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 警告: 未找到指定的配置ID: {configId}");
          return result;
        }
      }

      var enabledConfigs = dncConfigs.Where(c => c.Enabled).ToList();
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 启用的配置数量: {enabledConfigs.Count}");

      // 遍历每个DNC配置
      foreach (var dncConfig in enabledConfigs)
      {
        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 处理配置: {dncConfig.DeviceCode} ({dncConfig.DeviceName})");

        // 处理SEND文件夹
        if (string.IsNullOrEmpty(folderType) || folderType.ToUpper() == "SEND")
        {
          Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 获取SEND文件夹路径...");
          var sendPath = GetFtpDirectoryPath(dncConfig, "SEND");
          Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] SEND路径: {sendPath ?? "null"}");

          if (!string.IsNullOrEmpty(sendPath))
          {
            var sendFiles = GetFilesFromDirectory(sendPath, "SEND", dncConfig);
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] SEND文件夹找到 {sendFiles.Count} 个文件");

            if (sendFiles.Any())
            {
              var key = $"{dncConfig.DeviceCode}/SEND";
              result[key] = sendFiles;
              Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 添加到结果: {key} ({sendFiles.Count} 个文件)");

              // 输出文件详情
              foreach (var file in sendFiles)
              {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}]   - {file.FileName} ({file.Size}, {file.ModifiedTime:yyyy-MM-dd HH:mm:ss})");
              }
            }
            else
            {
              Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] SEND文件夹为空，未添加到结果");
            }
          }
          else
          {
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 警告: SEND路径为空，跳过处理");
          }
        }

        // 处理REC文件夹
        if (string.IsNullOrEmpty(folderType) || folderType.ToUpper() == "REC")
        {
          Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 获取REC文件夹路径...");
          var recPath = GetFtpDirectoryPath(dncConfig, "REC");
          Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] REC路径: {recPath ?? "null"}");

          if (!string.IsNullOrEmpty(recPath))
          {
            var recFiles = GetFilesFromDirectory(recPath, "REC", dncConfig);
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] REC文件夹找到 {recFiles.Count} 个文件");

            if (recFiles.Any())
            {
              var key = $"{dncConfig.DeviceCode}/REC";
              result[key] = recFiles;
              Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 添加到结果: {key} ({recFiles.Count} 个文件)");

              // 输出文件详情
              foreach (var file in recFiles)
              {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}]   - {file.FileName} ({file.Size}, {file.ModifiedTime:yyyy-MM-dd HH:mm:ss})");
              }
            }
          }
        }
      }

      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 完成处理，总共返回 {result.Count} 个文件夹，{result.Values.Sum(files => files.Count)} 个文件");
    }
    catch (Exception ex)
    {
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 错误: 获取FTP文件列表时出错");
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 异常信息: {ex.Message}");
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 堆栈跟踪: {ex.StackTrace}");
    }

    return result;
  }

  /// <summary>
  /// 获取FTP目录路径（由子类重写以提供具体实现）
  /// </summary>
  /// <param name="dncConfig">DNC配置</param>
  /// <param name="folderType">文件夹类型</param>
  /// <returns>目录路径</returns>
  protected virtual string GetFtpDirectoryPath(DncConfig dncConfig, string folderType)
  {
    // 基类提供默认实现，子类可以重写
    return "";
  }

  /// <summary>
  /// 从指定目录获取文件列表
  /// </summary>
  /// <param name="directoryPath">目录路径</param>
  /// <param name="folderType">文件夹类型</param>
  /// <param name="dncConfig">DNC配置</param>
  /// <returns>文件信息列表</returns>
  protected virtual List<FtpFileInfo> GetFilesFromDirectory(string directoryPath, string folderType, DncConfig dncConfig)
  {
    var fileList = new List<FtpFileInfo>();
    var pluginName = GetType().Name;

    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 开始扫描目录: {directoryPath}");

    try
    {
      // 确保目录存在
      if (!Directory.Exists(directoryPath))
      {
        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 目录不存在，正在创建: {directoryPath}");
        Directory.CreateDirectory(directoryPath);
        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 目录创建成功，返回空文件列表");
        return fileList; // 新创建的目录为空
      }

      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 目录存在，开始获取文件列表...");

      // 获取目录中的所有文件
      var files = Directory.GetFiles(directoryPath);
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 目录中找到 {files.Length} 个文件");

      // 处理每个文件，提取详细信息
      foreach (var file in files)
      {
        try
        {
          Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 处理文件: {Path.GetFileName(file)}");
          var fileInfo = new FileInfo(file);

          // 创建文件信息模型
          var fileModel = new FtpFileInfo
          {
            FileName = fileInfo.Name,
            ModifiedTime = fileInfo.LastWriteTime,
            Size = FormatFileSize(fileInfo.Length),
            FileType = fileInfo.Extension,
            FullPath = fileInfo.FullName,
            Source = directoryPath,
            ConfigId = dncConfig.Id.ToString(),
            DeviceCode = dncConfig.DeviceCode,
            FolderType = folderType
          };

          fileList.Add(fileModel);
          Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 文件信息: {fileModel.FileName}, 大小: {fileModel.Size}, 修改时间: {fileModel.ModifiedTime:yyyy-MM-dd HH:mm:ss}");
        }
        catch (Exception ex)
        {
          Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 错误: 处理文件时出错");
          Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 文件路径: {file}");
          Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 错误信息: {ex.Message}");
        }
      }

      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 目录扫描完成，成功处理 {fileList.Count} 个文件");
    }
    catch (Exception ex)
    {
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 错误: 读取目录时出错");
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 目录路径: {directoryPath}");
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 错误信息: {ex.Message}");
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{pluginName}] 堆栈跟踪: {ex.StackTrace}");
    }

    return fileList;
  }

  /// <summary>
  /// 格式化文件大小
  /// </summary>
  /// <param name="bytes">字节数</param>
  /// <returns>格式化后的文件大小</returns>
  protected virtual string FormatFileSize(long bytes)
  {
    if (bytes < 1024)
      return $"{bytes} B";
    else if (bytes < 1024 * 1024)
      return $"{bytes / 1024.0:F1} KB";
    else if (bytes < 1024 * 1024 * 1024)
      return $"{bytes / (1024.0 * 1024.0):F1} MB";
    else
      return $"{bytes / (1024.0 * 1024.0 * 1024.0):F1} GB";
  }

  /// <summary>
  /// 获取FTP服务器文件内容
  /// </summary>
  public virtual async Task<string> GetFileContent(string filePath)
  {
    return await Task.FromResult(string.Empty);
  }

  /// <summary>
  /// 设置DNC配置
  /// </summary>
  /// <param name="dncConfig">DNC配置</param>
  /// <returns>设置结果</returns>
  public virtual async Task SetDncConfigAsync(DncConfig dncConfig)
  {
    if (dncConfig == null)
      return;

    // 检查是否已存在相同ID的配置
    var existingConfig = _dncConfigs.FirstOrDefault(c => c.Id == dncConfig.Id);
    if (existingConfig != null)
    {
      // 更新已存在的配置
      var index = _dncConfigs.IndexOf(existingConfig);
      _dncConfigs[index] = dncConfig;
    }
    else
    {
      // 添加新配置
      _dncConfigs.Add(dncConfig);
    }

    await Task.CompletedTask;
  }

  /// <summary>
  /// 获取DNC配置
  /// </summary>
  /// <returns>DNC配置集合</returns>
  public virtual async Task<List<DncConfig>> GetDncConfigsAsync()
  {
    return await Task.FromResult(_dncConfigs);
  }

  /// <summary>
  /// 获取DNC配置
  /// </summary>
  /// <returns>DNC配置，如有多个则返回第一个</returns>
  public virtual async Task<DncConfig> GetDncConfigAsync()
  {
    return await Task.FromResult(_dncConfigs.FirstOrDefault());
  }

  /// <summary>
  /// 获取指定设备编码的DNC配置
  /// </summary>
  /// <param name="deviceCode">设备编码</param>
  /// <returns>DNC配置</returns>
  public virtual async Task<DncConfig> GetDncConfigByDeviceCodeAsync(string deviceCode)
  {
    return await Task.FromResult(_dncConfigs.FirstOrDefault(c => c.DeviceCode == deviceCode));
  }

  /// <summary>
  /// 移除DNC配置
  /// </summary>
  /// <param name="id">配置ID</param>
  /// <returns>操作结果</returns>
  public virtual async Task RemoveDncConfigAsync(long id)
  {
    var config = _dncConfigs.FirstOrDefault(c => c.Id == id);
    if (config != null)
    {
      _dncConfigs.Remove(config);
    }

    await Task.CompletedTask;
  }
}