using Furion.DependencyInjection;
using Furion.DynamicApiController;
using IotGateway.Plugin.Core.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace IotGateway.Plugin.Core;

/// <summary>
/// 插件服务器
/// </summary>
[ApiDescriptionSettings(Name = "PluginServer")]
[Route("plugins")]
public class PluginServer : ITransient, IDynamicApiController
{
  /// <summary>
  /// 插件管理器  
  /// </summary>
  private readonly PluginManager _pluginManager;

  /// <summary>
  /// 构造函数
  /// </summary>
  public PluginServer(PluginManager pluginManager)
  {
    _pluginManager = pluginManager;
  }

  /// <summary>
  /// 初始化插件服务
  /// </summary>
  [HttpPost("Initialize")]
  public async Task InitializeAsync()
  {
    await _pluginManager.InitializeAsync();
  }

  /// <summary>
  /// 获取所有插件列表
  /// </summary>
  /// <returns>插件信息列表</returns>
  [HttpGet("GetPlugins")]
  public List<PluginInfo> GetPlugins()
  {
    // 获取所有插件列表
    var plugins = _pluginManager.GetPlugins();
    // 转换为插件信息列表
    return plugins.Select(p => new PluginInfo
    {
      Name = p.Name, // 插件名称
      Version = p.Version, // 插件版本
      Description = p.Description, // 插件描述
      Enabled = p.Enabled, // 是否启用
      Category = p.Category // 插件分类
    }).ToList();
  }

  /// <summary>
  /// 获取已启用的插件下拉列表
  /// </summary>
  /// <returns>已启用的插件下拉数据</returns>
  [HttpGet("GetEnabledPlugins")]
  public List<PluginSelectItem> GetEnabledPlugins()
  {
    // 获取所有插件列表
    var plugins = _pluginManager.GetPlugins();
    // 过滤出已启用的插件，并转换为下拉列表数据格式
    return plugins
      .Where(p => p.Enabled) // 只选择已启用的插件
      .Select(p => new PluginSelectItem
      {
        Value = p.Name, // 值为插件名称
        Label = p.Name, // 显示名称为插件名称
        Description = p.Description, // 描述
        Category = p.Category // 分类
      }).ToList();
  }

  /// <summary>
  /// 上传并安装插件
  /// </summary>
  /// <param name="input">插件文件</param>
  [HttpPost("InstallPlugin")]
  public async Task InstallPluginAsync([FromForm] InstallPluginAsyncInput input)
  {
    try
    {
      Console.WriteLine($"开始处理插件上传请求，文件名: {input.File.FileName}, 大小: {input.File.Length} 字节");

      // 检查文件是否存在
      if (input.File == null || input.File.Length == 0)
      {
        Console.WriteLine("错误: 未收到有效的插件文件");
        throw new ArgumentException("未收到有效的插件文件");
      }

      // 获取文件扩展名
      string fileExtension = Path.GetExtension(input.File.FileName).ToLowerInvariant();
      Console.WriteLine($"文件扩展名: {fileExtension}");

      // 验证文件类型
      if (fileExtension != ".dll" && fileExtension != ".zip")
      {
        Console.WriteLine($"错误: 不支持的文件类型: {fileExtension}，仅支持.dll和.zip文件");
        throw new ArgumentException($"不支持的文件类型: {fileExtension}，仅支持.dll和.zip文件");
      }

      // 创建临时文件，保留原始扩展名
      // string originalFileName = Path.GetFileNameWithoutExtension(input.Files.FileName);
      string tempFileName = $"{Path.GetTempFileName()}.{fileExtension.TrimStart('.')}";
      Console.WriteLine($"创建临时文件: {tempFileName}");

      try
      {
        // 将上传的文件复制到临时文件
        Console.WriteLine("开始复制上传文件到临时文件...");
        using (var stream = new FileStream(tempFileName, FileMode.Create))
        {
          await input.File.CopyToAsync(stream);
        }

        // 验证临时文件
        if (!File.Exists(tempFileName))
        {
          Console.WriteLine($"错误: 临时文件创建失败: {tempFileName}");
          throw new IOException($"临时文件创建失败: {tempFileName}");
        }

        var fileInfo = new FileInfo(tempFileName);
        Console.WriteLine($"文件复制完成，临时文件大小: {fileInfo.Length} 字节");

        // 安装插件
        Console.WriteLine("开始安装插件...");
        await _pluginManager.InstallPluginAsync(tempFileName);
        Console.WriteLine("插件安装成功");
      }
      finally
      {
        // 删除临时文件
        if (File.Exists(tempFileName))
        {
          try
          {
            File.Delete(tempFileName);
            Console.WriteLine($"临时文件 {tempFileName} 已删除");
          }
          catch (Exception ex)
          {
            Console.WriteLine($"删除临时文件时出错: {ex.Message}");
          }
        }
      }
    }
    catch (Exception ex)
    {
      Console.WriteLine($"插件安装过程中发生异常: {ex.GetType().Name} - {ex.Message}");
      Console.WriteLine($"异常堆栈: {ex.StackTrace}");
      throw;
    }
  }

  /// <summary>
  /// 卸载插件
  /// </summary>
  /// <param name="pluginName">插件名称</param>
  [HttpPost("UninstallPlugin")]
  public async Task UninstallPluginAsync(string pluginName)
  {
    await _pluginManager.UninstallPluginAsync(pluginName);
  }

  /// <summary>
  /// 启动插件
  /// </summary>
  /// <param name="pluginName">插件名称</param>
  [HttpPost("StartPlugin")]
  public async Task StartPluginAsync(string pluginName)
  {
    await _pluginManager.StartPluginAsync(pluginName);
  }

  /// <summary>
  /// 停止插件
  /// </summary>
  /// <param name="pluginName">插件名称</param>
  [HttpPost("StopPlugin")]
  public async Task StopPluginAsync(string pluginName)
  {
    await _pluginManager.StopPluginAsync(pluginName);
  }

  /// <summary>
  /// 获取插件配置
  /// </summary>
  /// <param name="pluginName">插件名称</param>
  [HttpGet("GetPluginConfiguration")]
  public async Task<object> GetPluginConfigurationAsync(string pluginName)
  {
    var (schema, config) = await _pluginManager.GetPluginConfigurationAsync(pluginName);
    return new
    {
      Schema = schema,
      Config = config
    };
  }

  /// <summary>
  /// 启用/禁用插件
  /// </summary>
  /// <param name="input">插件名称</param>
  /// <returns>操作结果</returns>
  [HttpPost("TogglePluginEnable")]
  public async Task TogglePluginEnable(TogglePluginEnableInput input)
  {
    await _pluginManager.TogglePluginEnable(input.PluginName, input.Enable);
  }

  /// <summary>
  /// 更新插件配置
  /// </summary>
  /// <param name="pluginName">插件名称</param>
  /// <param name="configuration">配置</param>
  [HttpPost("UpdatePluginConfiguration")]
  public async Task UpdatePluginConfigurationAsync(string pluginName, [FromBody] object configuration)
  {
    await _pluginManager.UpdatePluginConfigurationAsync(pluginName, configuration);
  }

  /// <summary>
  /// 获取FTP服务器文件列表
  /// </summary>
  /// <param name="pluginName">插件名称</param>
  /// <param name="configId">配置ID</param>
  /// <param name="folderType">文件夹类型(SEND/REC)</param>
  /// <returns>以文件夹为键，文件信息列表为值的字典</returns>
  [HttpGet("GetFtpFiles")]
  public async Task<Dictionary<string, List<FtpFileInfo>>> GetFtpFiles([FromQuery] string pluginName, [FromQuery] string? configId = null, [FromQuery] string? folderType = null)
  {
    return await _pluginManager.GetFtpFiles(pluginName, configId, folderType);
  }

  /// <summary>
  /// 获取FTP服务器文件内容 
  /// </summary>
  /// <param name="pluginName">插件名称</param>
  /// <param name="input">文件路径</param>
  /// <returns>文件内容</returns>
  [HttpGet("GetFileContent")]
  public async Task<string> GetFileContent([FromQuery] string pluginName, [FromQuery] GetFileContentInput input)
  {
    return await _pluginManager.GetFileContent(pluginName, input.FilePath);
  }
}

/// <summary>
/// 安装插件输入
/// </summary>
public class InstallPluginAsyncInput
{
  /// <summary>
  /// 插件文件
  /// </summary>
  public IFormFile File { get; set; }
}

/// <summary> 
/// 获取FTP服务器文件内容输入
/// </summary>
public class GetFileContentInput
{

  /// <summary>
  /// 文件路径
  /// </summary>
  public string FilePath { get; set; } = string.Empty;
}

/// <summary>
/// 插件信息
/// </summary>
public class PluginInfo
{
  /// <summary>
  /// 插件名称
  /// </summary>
  public string Name { get; set; } = string.Empty;

  /// <summary>
  /// 插件版本
  /// </summary>
  public string Version { get; set; } = string.Empty;

  /// <summary>
  /// 插件描述
  /// </summary>
  public string Description { get; set; } = string.Empty;

  /// <summary>
  /// 是否启用
  /// </summary>
  public bool Enabled { get; set; }

  /// <summary>
  /// 插件分类
  /// </summary>
  public string Category { get; set; } = string.Empty;
}

/// <summary>
/// 插件下拉选择项
/// </summary>
public class PluginSelectItem
{
  /// <summary>
  /// 值
  /// </summary>
  public string Value { get; set; } = string.Empty;

  /// <summary>
  /// 显示标签
  /// </summary>
  public string Label { get; set; } = string.Empty;

  /// <summary>
  /// 描述
  /// </summary>
  public string Description { get; set; } = string.Empty;

  /// <summary>
  /// 分类
  /// </summary>
  public string Category { get; set; } = string.Empty;
}

/// <summary>
/// 启用/禁用插件输入
/// </summary>
public class TogglePluginEnableInput
{
  /// <summary>
  /// 插件名称
  /// </summary>
  public string PluginName { get; set; } = string.Empty;

  /// <summary>
  /// 是否启用
  /// </summary>
  public bool Enable { get; set; }
}
