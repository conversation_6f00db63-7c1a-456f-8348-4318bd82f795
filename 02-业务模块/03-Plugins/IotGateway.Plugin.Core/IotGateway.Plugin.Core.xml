<?xml version="1.0"?>
<doc>
    <assembly>
        <name>IotGateway.Plugin.Core</name>
    </assembly>
    <members>
        <member name="T:IotGateway.Plugin.Core.Constants">
            <summary>
            插件相关常量
            </summary>
        </member>
        <member name="F:IotGateway.Plugin.Core.Constants.PluginFolderName">
            <summary>
            插件目录名称
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.Constants.GetPluginPath">
            <summary>
            获取插件目录的完整路径
            </summary>
            <returns>插件目录完整路径</returns>
        </member>
        <member name="T:IotGateway.Plugin.Core.DncConfigServer.DncConfigService">
            <summary>
            DNC配置服务
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.DncConfigServer.DncConfigService.#ctor(Feng.IotGateway.Core.SqlSugar.SqlSugarRepository{IotGateway.Plugin.Core.Models.DncConfig},IotGateway.Plugin.Core.PluginManager)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.DncConfigServer.DncConfigService.Page(IotGateway.Plugin.Core.DncConfigServer.Dtos.PageDncConfigInput)">
            <summary>
            分页查询DNC配置
            </summary>
            <param name="input">查询条件</param>
            <returns>DNC配置列表</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.DncConfigServer.DncConfigService.GetDetail(System.Int64)">
            <summary>
            获取DNC配置详情
            </summary>
            <param name="id">主键Id</param>
            <returns>DNC配置详情</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.DncConfigServer.DncConfigService.Add(IotGateway.Plugin.Core.DncConfigServer.Dtos.AddDncConfigInput)">
            <summary>
            添加DNC配置
            </summary>
            <param name="input">添加参数</param>
            <returns></returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.DncConfigServer.DncConfigService.Update(IotGateway.Plugin.Core.DncConfigServer.Dtos.UpdateDncConfigInput)">
            <summary>
            更新DNC配置
            </summary>
            <param name="input">更新参数</param>
            <returns></returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.DncConfigServer.DncConfigService.Delete(IotGateway.Plugin.Core.DncConfigServer.Dtos.DeleteDncConfigInput)">
            <summary>
            删除DNC配置
            </summary>
            <param name="input">删除参数</param>
            <returns></returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.DncConfigServer.DncConfigService.BatchDelete(System.Collections.Generic.List{System.Int64})">
            <summary>
            批量删除DNC配置
            </summary>
            <param name="ids">主键Id集合</param>
            <returns></returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.DncConfigServer.DncConfigService.ChangeEnabled(IotGateway.Plugin.Core.DncConfigServer.Dtos.ChangeEnabledInput)">
            <summary>
            修改DNC配置启用状态
            </summary>
            <param name="input">修改参数</param>
            <returns></returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.DncConfigServer.DncConfigService.GetByPluginName(System.String)">
            <summary>
            根据插件名称获取DNC配置
            </summary>
            <param name="pluginName">插件名称</param>
            <returns>DNC配置</returns>
        </member>
        <member name="T:IotGateway.Plugin.Core.DncConfigServer.Dtos.DncConfigOutput">
            <summary>
            DNC配置输出
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.DncConfigOutput.Id">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.DncConfigOutput.DeviceCode">
            <summary>
            设备编号
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.DncConfigOutput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.DncConfigOutput.ProtocolType">
            <summary>
            协议类型
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.DncConfigOutput.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.DncConfigOutput.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.DncConfigOutput.AccessPermission">
            <summary>
            访问权限
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.DncConfigOutput.Enabled">
            <summary>
            启用状态
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.DncConfigOutput.Port">
            <summary>
            端口号
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.DncConfigOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.DncConfigOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.DncConfigOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.DncConfigServer.Dtos.AddDncConfigInput">
            <summary>
            DNC配置添加输入
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.AddDncConfigInput.DeviceCode">
            <summary>
            设备编号
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.AddDncConfigInput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.AddDncConfigInput.ProtocolType">
            <summary>
            协议类型
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.AddDncConfigInput.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.AddDncConfigInput.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.AddDncConfigInput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.AddDncConfigInput.AccessPermission">
            <summary>
            访问权限
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.AddDncConfigInput.Enabled">
            <summary>
            启用状态
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.AddDncConfigInput.Port">
            <summary>
            端口号
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.AddDncConfigInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.DncConfigServer.Dtos.UpdateDncConfigInput">
            <summary>
            DNC配置更新输入
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.UpdateDncConfigInput.Id">
            <summary>
            主键
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.DncConfigServer.Dtos.DeleteDncConfigInput">
            <summary>
            DNC配置删除输入
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.DeleteDncConfigInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.DncConfigServer.Dtos.PageDncConfigInput">
            <summary>
            DNC配置分页查询输入
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.PageDncConfigInput.Enabled">
            <summary>
            启用状态（空表示全部）
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.DncConfigServer.Dtos.ChangeEnabledInput">
            <summary>
            DNC配置启用/禁用输入
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.ChangeEnabledInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.DncConfigServer.Dtos.ChangeEnabledInput.Enabled">
            <summary>
            启用状态
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.IPlugin">
            <summary>
            插件接口
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.IPlugin.Name">
            <summary>
            插件名称
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.IPlugin.Version">
            <summary>
            插件版本
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.IPlugin.Description">
            <summary>
            插件描述
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.IPlugin.Enabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.IPlugin.Category">
            <summary>
            插件分类
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPlugin.InitializeAsync">
            <summary>
            初始化插件
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPlugin.StartAsync">
            <summary>
            启动插件
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPlugin.StopAsync">
            <summary>
            停止插件
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPlugin.GetConfigurationAsync">
            <summary>
            获取插件配置
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPlugin.UpdateConfigurationAsync(System.Object)">
            <summary>
            更新插件配置
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPlugin.GetConfigurationSchemaAsync">
            <summary>
            获取插件配置Schema
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPlugin.ToggleEnableAsync(System.Boolean)">
            <summary>
            启用或禁用插件
            </summary>
            <param name="enable">是否启用</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPlugin.GetFtpFiles(System.String,System.String)">
            <summary>
            获取FTP服务器文件列表
            </summary>
            <param name="configId">配置ID</param>
            <param name="folderType">文件夹类型(SEND/REC)</param>
            <returns>以文件夹为键，文件信息列表为值的字典</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPlugin.GetFileContent(System.String)">
            <summary>
            获取FTP服务器文件内容
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPlugin.SetDncConfigAsync(IotGateway.Plugin.Core.Models.DncConfig)">
            <summary>
            设置DNC配置
            </summary>
            <param name="dncConfig">DNC配置</param>
            <returns>设置结果</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPlugin.GetDncConfigAsync">
            <summary>
            获取DNC配置
            </summary>
            <returns>DNC配置，如有多个则返回第一个</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPlugin.GetDncConfigsAsync">
            <summary>
            获取DNC配置集合
            </summary>
            <returns>DNC配置集合</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPlugin.GetDncConfigByDeviceCodeAsync(System.String)">
            <summary>
            获取指定设备编码的DNC配置
            </summary>
            <param name="deviceCode">设备编码</param>
            <returns>DNC配置</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPlugin.RemoveDncConfigAsync(System.Int64)">
            <summary>
            移除DNC配置
            </summary>
            <param name="id">配置ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:IotGateway.Plugin.Core.IPluginConfig">
            <summary>
            插件配置接口
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.IPluginConfig.Enabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.IPluginConfig.GetConfigurationSchema">
            <summary>
            获取配置Schema
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.Models.DncConfig">
            <summary>
            DNC配置实体
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.DncConfig.DeviceCode">
            <summary>
            设备编号
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.DncConfig.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.DncConfig.ProtocolType">
            <summary>
            协议类型
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.DncConfig.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.DncConfig.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.DncConfig.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.DncConfig.AccessPermission">
            <summary>
            访问权限
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.DncConfig.Enabled">
            <summary>
            启用状态
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.DncConfig.Port">
            <summary>
            端口号
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.DncConfig.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.DncConfig.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.DncConfig.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.Models.FtpFileInfo">
            <summary>
            FTP文件信息模型
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.FtpFileInfo.FileName">
            <summary>
            文件名称
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.FtpFileInfo.ModifiedTime">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.FtpFileInfo.Size">
            <summary>
            文件大小（字节）
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.FtpFileInfo.FileType">
            <summary>
            文件类型（扩展名）
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.FtpFileInfo.FullPath">
            <summary>
            完整文件路径
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.FtpFileInfo.Source">
            <summary>
            源文件路径
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.FtpFileInfo.ConfigId">
            <summary>
            配置ID
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.FtpFileInfo.DeviceCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.Models.FtpFileInfo.FolderType">
            <summary>
            文件夹类型(SEND/REC)
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.PluginBase">
            <summary>
            插件基类
            </summary>
        </member>
        <member name="F:IotGateway.Plugin.Core.PluginBase._enabled">
            <summary>
            是否启用  
            </summary>
        </member>
        <member name="F:IotGateway.Plugin.Core.PluginBase._configuration">
            <summary>
            配置
            </summary>
        </member>
        <member name="F:IotGateway.Plugin.Core.PluginBase._dncConfigs">
            <summary>
            DNC配置集合
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.PluginBase.Name">
            <summary>
            插件名称
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.PluginBase.Version">
            <summary>
            插件版本
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.PluginBase.Description">
            <summary>
            插件描述
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.PluginBase.Category">
            <summary>
            插件分类
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.PluginBase.Enabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginBase.InitializeAsync">
            <summary>
            初始化
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginBase.StartAsync">
            <summary>
            启动
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginBase.StopAsync">
            <summary>
            停止
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginBase.GetConfigurationAsync">
            <summary>
            获取配置
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginBase.GetConfigurationSchemaAsync">
            <summary>
            获取配置Schema
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginBase.UpdateConfigurationAsync(System.Object)">
            <summary>
            更新配置
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginBase.ToggleEnableAsync(System.Boolean)">
            <summary>
            启用或禁用插件
            </summary>
            <param name="enable">是否启用</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginBase.GetFtpFiles(System.String,System.String)">
            <summary>
            获取FTP服务器文件列表
            </summary>
            <param name="configId">配置ID</param>
            <param name="folderType">文件夹类型(SEND/REC)</param>
            <returns>以文件夹为键，文件信息列表为值的字典</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginBase.GetFileContent(System.String)">
            <summary>
            获取FTP服务器文件内容
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginBase.SetDncConfigAsync(IotGateway.Plugin.Core.Models.DncConfig)">
            <summary>
            设置DNC配置
            </summary>
            <param name="dncConfig">DNC配置</param>
            <returns>设置结果</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginBase.GetDncConfigsAsync">
            <summary>
            获取DNC配置
            </summary>
            <returns>DNC配置集合</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginBase.GetDncConfigAsync">
            <summary>
            获取DNC配置
            </summary>
            <returns>DNC配置，如有多个则返回第一个</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginBase.GetDncConfigByDeviceCodeAsync(System.String)">
            <summary>
            获取指定设备编码的DNC配置
            </summary>
            <param name="deviceCode">设备编码</param>
            <returns>DNC配置</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginBase.RemoveDncConfigAsync(System.Int64)">
            <summary>
            移除DNC配置
            </summary>
            <param name="id">配置ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:IotGateway.Plugin.Core.PluginHostedService">
            <summary>
            插件托管服务
            </summary>
        </member>
        <member name="F:IotGateway.Plugin.Core.PluginHostedService._logger">
            <summary>
            日志记录器
            </summary>
        </member>
        <member name="F:IotGateway.Plugin.Core.PluginHostedService._pluginLoader">
            <summary>
            插件加载器
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginHostedService.#ctor(Microsoft.Extensions.Logging.ILogger{IotGateway.Plugin.Core.PluginHostedService},IotGateway.Plugin.Core.PluginLoader)">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
            <param name="pluginLoader">插件加载器</param>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginHostedService.StartAsync(System.Threading.CancellationToken)">
            <summary>
            启动插件托管服务
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginHostedService.StopAsync(System.Threading.CancellationToken)">
            <summary>
            停止插件托管服务
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="T:IotGateway.Plugin.Core.PluginLoader">
            <summary>
            插件加载器
            </summary>
        </member>
        <member name="F:IotGateway.Plugin.Core.PluginLoader._pluginPath">
            <summary>
            插件目录
            </summary>
        </member>
        <member name="F:IotGateway.Plugin.Core.PluginLoader._loadedPlugins">
            <summary>
            已加载的插件列表
            </summary>
        </member>
        <member name="F:IotGateway.Plugin.Core.PluginLoader._db">
            <summary>
            DNC配置仓储
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginLoader.#ctor(SqlSugar.ISqlSugarClient)">
            <summary>
            构造函数
            </summary>
            <param name="db">DNC配置仓储</param>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginLoader.LoadPluginsAsync">
            <summary>
            加载插件
            </summary>
            <returns>加载的插件列表</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginLoader.GetPluginFiles(System.String)">
            <summary>
            递归获取目录下所有dll文件
            </summary>
            <param name="directory">起始目录</param>
            <returns>所有dll文件路径</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginLoader.InstallPluginAsync(System.String)">
            <summary>
            安装插件
            </summary>
            <param name="pluginFile">插件文件路径</param>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginLoader.InstallZipPluginAsync(System.String)">
            <summary>
            安装ZIP格式的插件包
            </summary>
            <param name="zipFile">ZIP文件路径</param>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginLoader.UninstallPluginAsync(System.String)">
            <summary>
            卸载插件
            </summary>
            <param name="pluginName">插件名称</param>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginLoader.GetLoadedPlugins">
            <summary>
            获取已加载的插件
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginLoader.GetPlugin(System.String)">
            <summary>
            获取指定插件
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginLoader.InitializePluginAsync(IotGateway.Plugin.Core.IPlugin)">
            <summary>
            初始化插件
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.PluginManager">
            <summary>
            插件管理器
            </summary>
        </member>
        <member name="F:IotGateway.Plugin.Core.PluginManager._pluginLoader">
            <summary>
            插件加载器
            </summary>
        </member>
        <member name="F:IotGateway.Plugin.Core.PluginManager._dncConfigRepository">
            <summary>
            DNC配置仓储
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.#ctor(IotGateway.Plugin.Core.PluginLoader,Feng.IotGateway.Core.SqlSugar.SqlSugarRepository{IotGateway.Plugin.Core.Models.DncConfig})">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.InitializeAsync">
            <summary>
            初始化插件管理器并自动启动配置的插件
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.InstallPluginAsync(System.String)">
            <summary>
            上传并安装插件
            </summary>
            <param name="pluginFile">插件文件路径</param>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.UninstallPluginAsync(System.String)">
            <summary>
            卸载插件
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.StartPluginAsync(System.String)">
            <summary>
            启动插件
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.StopPluginAsync(System.String)">
            <summary>
            停止插件
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.TogglePluginEnable(System.String,System.Boolean)">
            <summary>
            启用/禁用插件
            </summary>
            <param name="pluginName">插件名称</param>
            <param name="enable">是否启用</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.GetPluginConfigurationAsync(System.String)">
            <summary>
            获取插件配置
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.UpdatePluginConfigurationAsync(System.String,System.Object)">
            <summary>
            更新插件配置
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.GetPlugins">
            <summary>
            获取所有已加载的插件
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.GetFtpFiles(System.String,System.String,System.String)">
            <summary>
            获取FTP服务器文件列表
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.GetFileContent(System.String,System.String)">
            <summary>
            获取FTP服务器文件内容
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.GetDncConfigsByPluginNameAsync(System.String)">
            <summary>
            根据插件名称获取DNC配置列表
            </summary>
            <param name="pluginName">插件名称</param>
            <returns>DNC配置列表</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.GetDncConfigsAsync(System.Linq.Expressions.Expression{System.Func{IotGateway.Plugin.Core.Models.DncConfig,System.Boolean}})">
            <summary>
            根据条件获取DNC配置列表
            </summary>
            <param name="predicate">查询条件</param>
            <returns>DNC配置列表</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginManager.UpdateDncConfigToPluginAsync(IotGateway.Plugin.Core.Models.DncConfig)">
            <summary>
            更新DNC配置到插件
            </summary>
            <param name="dncConfig">DNC配置</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:IotGateway.Plugin.Core.PluginServer">
            <summary>
            插件服务器
            </summary>
        </member>
        <member name="F:IotGateway.Plugin.Core.PluginServer._pluginManager">
            <summary>
            插件管理器  
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginServer.#ctor(IotGateway.Plugin.Core.PluginManager)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginServer.InitializeAsync">
            <summary>
            初始化插件服务
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginServer.GetPlugins">
            <summary>
            获取所有插件列表
            </summary>
            <returns>插件信息列表</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginServer.GetEnabledPlugins">
            <summary>
            获取已启用的插件下拉列表
            </summary>
            <returns>已启用的插件下拉数据</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginServer.InstallPluginAsync(IotGateway.Plugin.Core.InstallPluginAsyncInput)">
            <summary>
            上传并安装插件
            </summary>
            <param name="input">插件文件</param>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginServer.UninstallPluginAsync(System.String)">
            <summary>
            卸载插件
            </summary>
            <param name="pluginName">插件名称</param>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginServer.StartPluginAsync(System.String)">
            <summary>
            启动插件
            </summary>
            <param name="pluginName">插件名称</param>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginServer.StopPluginAsync(System.String)">
            <summary>
            停止插件
            </summary>
            <param name="pluginName">插件名称</param>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginServer.GetPluginConfigurationAsync(System.String)">
            <summary>
            获取插件配置
            </summary>
            <param name="pluginName">插件名称</param>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginServer.TogglePluginEnable(IotGateway.Plugin.Core.TogglePluginEnableInput)">
            <summary>
            启用/禁用插件
            </summary>
            <param name="input">插件名称</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginServer.UpdatePluginConfigurationAsync(System.String,System.Object)">
            <summary>
            更新插件配置
            </summary>
            <param name="pluginName">插件名称</param>
            <param name="configuration">配置</param>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginServer.GetFtpFiles(System.String,System.String,System.String)">
            <summary>
            获取FTP服务器文件列表
            </summary>
            <param name="pluginName">插件名称</param>
            <param name="configId">配置ID</param>
            <param name="folderType">文件夹类型(SEND/REC)</param>
            <returns>以文件夹为键，文件信息列表为值的字典</returns>
        </member>
        <member name="M:IotGateway.Plugin.Core.PluginServer.GetFileContent(System.String,IotGateway.Plugin.Core.GetFileContentInput)">
            <summary>
            获取FTP服务器文件内容 
            </summary>
            <param name="pluginName">插件名称</param>
            <param name="input">文件路径</param>
            <returns>文件内容</returns>
        </member>
        <member name="T:IotGateway.Plugin.Core.InstallPluginAsyncInput">
            <summary>
            安装插件输入
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.InstallPluginAsyncInput.File">
            <summary>
            插件文件
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.GetFileContentInput">
            <summary> 
            获取FTP服务器文件内容输入
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.GetFileContentInput.FilePath">
            <summary>
            文件路径
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.PluginInfo">
            <summary>
            插件信息
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.PluginInfo.Name">
            <summary>
            插件名称
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.PluginInfo.Version">
            <summary>
            插件版本
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.PluginInfo.Description">
            <summary>
            插件描述
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.PluginInfo.Enabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.PluginInfo.Category">
            <summary>
            插件分类
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.PluginSelectItem">
            <summary>
            插件下拉选择项
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.PluginSelectItem.Value">
            <summary>
            值
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.PluginSelectItem.Label">
            <summary>
            显示标签
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.PluginSelectItem.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.PluginSelectItem.Category">
            <summary>
            分类
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.TogglePluginEnableInput">
            <summary>
            启用/禁用插件输入
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.TogglePluginEnableInput.PluginName">
            <summary>
            插件名称
            </summary>
        </member>
        <member name="P:IotGateway.Plugin.Core.TogglePluginEnableInput.Enable">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:IotGateway.Plugin.Core.Startup">
            <summary>
            
            </summary>
        </member>
        <member name="M:IotGateway.Plugin.Core.Startup.ConfigureServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            
            </summary>
            <param name="services"></param>
        </member>
        <member name="T:DncService">
            <summary>
                DNC服务
            </summary>
        </member>
        <member name="M:DncService.GetFiles(System.String)">
            <summary>
                获取指定目录下的文件列表
            </summary>
            <param name="deviceCode">设备编码（必填）</param>
            <returns>文件信息列表，包含文件名、大小、类型等</returns>
        </member>
        <member name="M:DncService.GetDirectoryFiles(System.String,System.String)">
            <summary>
            获取指定目录下的文件列表
            </summary>
            <param name="source">目录名(SEND或REC)</param>
            <param name="deviceCode">设备编码（必填）</param>
            <returns>文件信息列表</returns>
        </member>
        <member name="M:DncService.PreviewFile(System.String,System.String,System.String)">
            <summary>
                预览文件内容
            </summary>
            <param name="FileName">文件名</param>
            <param name="source">文件来源目录(SEND或REC)，默认为SEND</param>
            <param name="deviceCode">设备编码（必填）</param>
            <returns>文件内容或Base64编码的二进制内容</returns>
        </member>
        <member name="M:DncService.BatchPreviewFiles(BatchPreviewRequest)">
            <summary>
                批量预览文件内容
            </summary>
            <param name="request">批量预览请求</param>
            <returns>文件内容映射</returns>
        </member>
        <member name="M:DncService.BatchBackupFiles(BatchFileRequest)">
            <summary>
                批量备份文件
            </summary>
            <param name="request">批量备份请求</param>
            <returns>备份结果</returns>
        </member>
        <member name="M:DncService.BatchDeleteFiles(BatchFileRequest)">
            <summary>
                批量删除文件
            </summary>
            <param name="request">批量删除请求</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:DncService.UploadFile(UploadFile)">
            <summary>
                上传文件
            </summary>
            <returns>上传结果</returns>
        </member>
        <member name="M:DncService.GetFtpPath(System.String,System.String,System.String)">
            <summary>
            获取FTP文件路径
            </summary>
            <param name="deviceCode">设备编码</param>
            <param name="fileName">文件名</param>
            <param name="source">来源目录(SEND或REC)</param>
            <returns>完整文件路径</returns>
        </member>
        <member name="T:UploadFile">
            <summary>
                上传文件请求
            </summary>
        </member>
        <member name="P:UploadFile.Content">
            <summary>
                文件内容
            </summary>
        </member>
        <member name="P:UploadFile.FileName">
            <summary>
                文件名
            </summary>
        </member>
        <member name="P:UploadFile.DeviceCode">
            <summary>
                设备编码（必填）
            </summary>
        </member>
        <member name="T:FileItem">
            <summary>
                文件项目
            </summary>
        </member>
        <member name="P:FileItem.FileName">
            <summary>
                文件名
            </summary>
        </member>
        <member name="P:FileItem.Source">
            <summary>
                文件来源目录(SEND或REC)，默认为SEND
            </summary>
        </member>
        <member name="T:BatchFileRequest">
            <summary>
                批量文件操作请求
            </summary>
        </member>
        <member name="P:BatchFileRequest.FileNames">
            <summary>
                文件列表，每个文件包含文件名和来源目录
            </summary>
        </member>
        <member name="P:BatchFileRequest.DeviceCode">
            <summary>
                设备编码（必填）
            </summary>
        </member>
        <member name="T:BatchPreviewRequest">
            <summary>
                批量预览请求
            </summary>
        </member>
        <member name="P:BatchPreviewRequest.IsBinary">
            <summary>
                是否为二进制文件，默认为false
            </summary>
        </member>
        <member name="T:BatchPreviewResult">
            <summary>
                批量预览结果
            </summary>
        </member>
        <member name="P:BatchPreviewResult.FileContents">
            <summary>
                文件内容映射，键为文件名，值为文件内容
            </summary>
        </member>
        <member name="P:BatchPreviewResult.FailedFiles">
            <summary>
                失败的文件，键为文件名，值为错误信息
            </summary>
        </member>
        <member name="T:BatchOperationResult">
            <summary>
                批量操作结果
            </summary>
        </member>
        <member name="P:BatchOperationResult.Success">
            <summary>
                操作是否成功
            </summary>
        </member>
        <member name="P:BatchOperationResult.Message">
            <summary>
                操作结果消息
            </summary>
        </member>
        <member name="P:BatchOperationResult.SuccessFiles">
            <summary>
                成功处理的文件列表
            </summary>
        </member>
        <member name="P:BatchOperationResult.FailedFiles">
            <summary>
                处理失败的文件，键为文件名，值为错误信息
            </summary>
        </member>
    </members>
</doc>
